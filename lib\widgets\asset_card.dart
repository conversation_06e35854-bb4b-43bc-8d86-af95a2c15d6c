import 'package:flutter/material.dart';
import '../models/asset.dart';

class AssetCard extends StatelessWidget {
  final Asset asset;
  final VoidCallback? onTap;

  const AssetCard({
    super.key,
    required this.asset,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey[100]!, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 资产基本信息行
                Row(
                  children: [
                    // 资产图标
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(asset.category).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: _getCategoryColor(asset.category).withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        _getCategoryIcon(asset.category),
                        color: _getCategoryColor(asset.category),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 资产名称和编号
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            asset.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              fontSize: 18,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            asset.assetNumber,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 状态标签
                    _buildStatusChip(asset.status),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 资产详细信息
                if (asset.brand != null || asset.model != null) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline_rounded,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          [asset.brand, asset.model].where((e) => e != null && e.isNotEmpty).join(' '),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                
                // 位置信息
                if (asset.location != null && asset.location!.isNotEmpty) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_rounded,
                        size: 16,
                        color: Colors.grey[500],
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          asset.location!,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                ],
                
                // 标签行
                Wrap(
                  spacing: 12,
                  runSpacing: 10,
                  children: [
                    _buildCategoryChip(asset.category),
                    if (asset.assignedTo != null && asset.assignedTo!.isNotEmpty)
                      _buildAssignedChip(asset.assignedTo!),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(AssetStatus status) {
    MaterialColor color;
    IconData icon;
    String label;

    switch (status) {
      case AssetStatus.available:
        color = Colors.green;
        icon = Icons.check_circle_rounded;
        label = '可用';
        break;
      case AssetStatus.assigned:
        color = Colors.blue;
        icon = Icons.person_rounded;
        label = '已分配';
        break;
      case AssetStatus.maintenance:
        color = Colors.orange;
        icon = Icons.build_rounded;
        label = '维护中';
        break;
      case AssetStatus.retired:
        color = Colors.grey;
        icon = Icons.delete_forever_rounded;
        label = '已报废';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color[700],
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: color[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(AssetCategory category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getCategoryColor(category).withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: _getCategoryColor(category).withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getCategoryIcon(category),
            size: 14,
            color: _getCategoryColor(category)[700],
          ),
          const SizedBox(width: 6),
          Text(
            _getCategoryDisplayName(category),
            style: TextStyle(
              color: _getCategoryColor(category)[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignedChip(String assignedTo) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.indigo.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.indigo.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.person_rounded,
            size: 14,
            color: Colors.indigo[700],
          ),
          const SizedBox(width: 6),
          Text(
            assignedTo,
            style: TextStyle(
              color: Colors.indigo[700],
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  MaterialColor _getCategoryColor(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Colors.blue;
      case AssetCategory.desktop:
        return Colors.indigo;
      case AssetCategory.monitor:
        return Colors.purple;
      case AssetCategory.printer:
        return Colors.green;
      case AssetCategory.phone:
        return Colors.orange;
      case AssetCategory.tablet:
        return Colors.teal;
      case AssetCategory.server:
        return Colors.red;
      case AssetCategory.network:
        return Colors.cyan;
      case AssetCategory.other:
        return Colors.grey;
    }
  }

  IconData _getCategoryIcon(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return Icons.laptop;
      case AssetCategory.desktop:
        return Icons.computer;
      case AssetCategory.monitor:
        return Icons.monitor;
      case AssetCategory.printer:
        return Icons.print;
      case AssetCategory.phone:
        return Icons.phone_android;
      case AssetCategory.tablet:
        return Icons.tablet;
      case AssetCategory.server:
        return Icons.dns;
      case AssetCategory.network:
        return Icons.router;
      case AssetCategory.other:
        return Icons.category;
    }
  }

  String _getCategoryDisplayName(AssetCategory category) {
    switch (category) {
      case AssetCategory.laptop:
        return '笔记本电脑';
      case AssetCategory.desktop:
        return '台式电脑';
      case AssetCategory.monitor:
        return '显示器';
      case AssetCategory.printer:
        return '打印机';
      case AssetCategory.phone:
        return '手机';
      case AssetCategory.tablet:
        return '平板电脑';
      case AssetCategory.server:
        return '服务器';
      case AssetCategory.network:
        return '网络设备';
      case AssetCategory.other:
        return '其他';
    }
  }
}
