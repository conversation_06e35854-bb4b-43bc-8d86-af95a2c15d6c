import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/user_main_layout.dart';
import '../config/routes.dart';
import '../models/ticket.dart';
import '../services/ticket_service.dart';

class TicketCreateScreen extends StatefulWidget {
  const TicketCreateScreen({super.key});

  @override
  State<TicketCreateScreen> createState() => _TicketCreateScreenState();
}

class _TicketCreateScreenState extends State<TicketCreateScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  final TicketService _ticketService = TicketService();
  
  String _selectedPriority = TicketPriority.medium;
  String _selectedCategory = TicketCategory.other;
  bool _isLoading = false;

  final List<String> _priorities = [
    TicketPriority.low,
    TicketPriority.medium,
    TicketPriority.high,
  ];

  final List<String> _categories = [
    TicketCategory.hardware,
    TicketCategory.software,
    TicketCategory.network,
    TicketCategory.account,
    TicketCategory.other,
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UserMainLayout(
      currentRoute: AppRoutes.ticketCreate,
      title: '创建工单',
      showBackButton: true,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormCard(),
              const SizedBox(height: 24),
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '工单信息',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          
          // 标题
          TextFormField(
            controller: _titleController,
            decoration: const InputDecoration(
              labelText: '问题标题 *',
              hintText: '请简要描述您遇到的问题',
              prefixIcon: Icon(Icons.title),
              border: OutlineInputBorder(),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入问题标题';
              }
              if (value.trim().length < 5) {
                return '标题至少需要5个字符';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // 类别
          DropdownButtonFormField<String>(
            value: _selectedCategory,
            decoration: const InputDecoration(
              labelText: '问题类别 *',
              prefixIcon: Icon(Icons.category),
              border: OutlineInputBorder(),
            ),
            items: _categories.map((category) {
              return DropdownMenuItem(
                value: category,
                child: Text(_getCategoryDisplayName(category)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedCategory = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          
          // 优先级
          DropdownButtonFormField<String>(
            value: _selectedPriority,
            decoration: const InputDecoration(
              labelText: '优先级 *',
              prefixIcon: Icon(Icons.priority_high),
              border: OutlineInputBorder(),
            ),
            items: _priorities.map((priority) {
              return DropdownMenuItem(
                value: priority,
                child: Row(
                  children: [
                    _getPriorityIcon(priority),
                    const SizedBox(width: 8),
                    Text(_getPriorityDisplayName(priority)),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedPriority = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          
          // 问题描述
          TextFormField(
            controller: _descriptionController,
            maxLines: 6,
            decoration: const InputDecoration(
              labelText: '问题描述 *',
              hintText: '请详细描述您遇到的问题，包括：\n1. 问题发生的具体情况\n2. 错误信息（如有）\n3. 已尝试的解决方法\n4. 问题对工作的影响',
              prefixIcon: Icon(Icons.description),
              border: OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入问题描述';
              }
              if (value.trim().length < 20) {
                return '问题描述至少需要20个字符';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          
          // 提示信息
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.withOpacity(0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '提交工单后，IT团队会尽快处理您的请求。您可以在"IT工单"页面查看处理进度。',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitTicket,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '提交工单',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _getPriorityIcon(String priority) {
    switch (priority) {
      case TicketPriority.high:
        return Icon(Icons.keyboard_arrow_up, color: Colors.red[600]);
      case TicketPriority.medium:
        return Icon(Icons.remove, color: Colors.orange[600]);
      case TicketPriority.low:
        return Icon(Icons.keyboard_arrow_down, color: Colors.green[600]);
      default:
        return Icon(Icons.remove, color: Colors.grey[600]);
    }
  }

  String _getPriorityDisplayName(String priority) {
    switch (priority) {
      case TicketPriority.high:
        return '高优先级';
      case TicketPriority.medium:
        return '中等优先级';
      case TicketPriority.low:
        return '低优先级';
      default:
        return priority;
    }
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case TicketCategory.hardware:
        return '硬件问题';
      case TicketCategory.software:
        return '软件问题';
      case TicketCategory.network:
        return '网络问题';
      case TicketCategory.account:
        return '账户问题';
      case TicketCategory.other:
        return '其他问题';
      default:
        return category;
    }
  }

  Future<void> _submitTicket() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final request = CreateTicketRequest(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        category: _selectedCategory,
      );

      await _ticketService.createTicket(request);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('工单提交成功！'),
            backgroundColor: Colors.green,
          ),
        );
        
        // 返回工单列表页面
        context.go(AppRoutes.ticketList);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('提交失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
