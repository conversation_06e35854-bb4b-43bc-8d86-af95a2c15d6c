import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../widgets/loading_widget.dart';
import '../widgets/success_animation.dart';

class UserFormScreen extends StatefulWidget {
  final User? user;

  const UserFormScreen({Key? key, this.user}) : super(key: key);

  @override
  State<UserFormScreen> createState() => _UserFormScreenState();
}

class _UserFormScreenState extends State<UserFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();
  final ScrollController _scrollController = ScrollController();

  // Form controllers
  final _usernameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _departmentController = TextEditingController();
  String? _selectedDepartment;

  // Form field keys for scrolling to errors
  final _usernameKey = GlobalKey();
  final _emailKey = GlobalKey();
  final _passwordKey = GlobalKey();
  final _confirmPasswordKey = GlobalKey();

  String _selectedRole = 'Normal User';
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  // Password validation states
  String _passwordError = '';
  String _confirmPasswordError = '';
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.red;

  List<String> _roles = ['Admin', 'Normal User'];
  List<String> _departments = [];

  bool get _isEditing => widget.user != null;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    if (_isEditing) {
      _populateForm();
    }
    
    // 添加密码实时验证监听器
    _passwordController.addListener(_validatePasswordRealtime);
    _confirmPasswordController.addListener(_validateConfirmPasswordRealtime);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    _departmentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    try {
      final departments = await _userService.getDepartments();

      // 添加默认部门选项
      final defaultDepartments = [
        'IT部门',
        '财务部门',
        '人事部门',
        '市场部门',
        '销售部门',
        '运营部门',
        '客服部门',
        '研发部门',
        '行政部门',
        '法务部门',
      ];

      // 合并API返回的部门和默认部门，去重
      final allDepartments = <String>{};
      allDepartments.addAll(departments);
      allDepartments.addAll(defaultDepartments);

      setState(() {
        _departments = allDepartments.toList()..sort();
      });
    } catch (e) {
      // 如果API失败，至少提供默认部门
      setState(() {
        _departments = [
          'IT部门',
          '财务部门',
          '人事部门',
          '市场部门',
          '销售部门',
          '运营部门',
          '客服部门',
          '研发部门',
          '行政部门',
          '法务部门',
        ];
      });
    }
  }

  void _populateForm() {
    final user = widget.user!;
    _usernameController.text = user.username;
    _emailController.text = user.email;
    _fullNameController.text = user.fullName ?? '';
    _selectedDepartment = user.department;
    _selectedRole = user.role;
  }

  // 实时密码验证
  void _validatePasswordRealtime() {
    final password = _passwordController.text;
    setState(() {
      _passwordStrength = _calculatePasswordStrength(password);
      _passwordStrengthText = _getPasswordStrengthText(_passwordStrength);
      _passwordStrengthColor = _getPasswordStrengthColor(_passwordStrength);
      
      if (password.isNotEmpty && password.length < 6) {
        _passwordError = '密码至少需要6个字符';
      } else {
        _passwordError = '';
      }
    });
    
    // 如果确认密码已输入，也要重新验证一致性
    if (_confirmPasswordController.text.isNotEmpty) {
      _validateConfirmPasswordRealtime();
    }
  }

  // 实时确认密码验证
  void _validateConfirmPasswordRealtime() {
    final password = _passwordController.text;
    final confirmPassword = _confirmPasswordController.text;
    
    setState(() {
      if (confirmPassword.isNotEmpty && confirmPassword != password) {
        _confirmPasswordError = '两次输入的密码不一致';
      } else {
        _confirmPasswordError = '';
      }
    });
  }

  // 计算密码强度
  double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;
    
    double strength = 0.0;
    
    // 长度检查
    if (password.length >= 6) strength += 0.2;
    if (password.length >= 8) strength += 0.1;
    if (password.length >= 12) strength += 0.1;
    
    // 包含小写字母
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.15;
    
    // 包含大写字母
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.15;
    
    // 包含数字
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.15;
    
    // 包含特殊字符
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.15;
    
    return strength.clamp(0.0, 1.0);
  }

  String _getPasswordStrengthText(double strength) {
    if (strength == 0.0) return '';
    if (strength < 0.3) return '弱';
    if (strength < 0.6) return '中等';
    if (strength < 0.8) return '强';
    return '很强';
  }

  Color _getPasswordStrengthColor(double strength) {
    if (strength == 0.0) return Colors.grey;
    if (strength < 0.3) return Colors.red;
    if (strength < 0.6) return Colors.orange;
    if (strength < 0.8) return Colors.blue;
    return Colors.green;
  }

  // 滚动到第一个错误字段
  void _scrollToFirstError() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = _formKey.currentContext;
      if (context == null) return;

      // 检查各个字段的验证状态并滚动到第一个错误
      if (_validateUsername(_usernameController.text) != null) {
        _scrollToWidget(_usernameKey);
        return;
      }
      if (_validateEmail(_emailController.text) != null) {
        _scrollToWidget(_emailKey);
        return;
      }
      if (!_isEditing && _validatePassword(_passwordController.text) != null) {
        _scrollToWidget(_passwordKey);
        return;
      }
      if (!_isEditing && _validateConfirmPassword(_confirmPasswordController.text) != null) {
        _scrollToWidget(_confirmPasswordKey);
        return;
      }
    });
  }

  void _scrollToWidget(GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        alignment: 0.1, // 滚动到屏幕顶部10%的位置
      );
    }
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      _scrollToFirstError();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      if (_isEditing) {
        // Update existing user - 只更新管理员可以修改的字段
        final request = UpdateUserRequest(
          username: widget.user!.username, // 保持原用户名不变
          email: _emailController.text.trim(),
          role: _selectedRole,
          fullName: widget.user!.fullName, // 保持原姓名不变
          department: _selectedDepartment,
        );

        await _userService.updateUser(widget.user!.id, request);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '更新成功',
          message: '用户信息已成功更新',
          onComplete: () {
            Navigator.of(context).pop(true);
          },
        );
        return;
      } else {
        // Create new user
        final request = CreateUserRequest(
          username: _usernameController.text.trim(),
          email: _selectedRole == 'Admin'
              ? '<EMAIL>' // 管理员使用系统默认邮箱
              : _emailController.text.trim(),
          password: _passwordController.text,
          role: _selectedRole,
          fullName: _selectedRole == 'Admin'
              ? null
              : (_fullNameController.text.trim().isEmpty
                  ? null
                  : _fullNameController.text.trim()),
          department: _selectedRole == 'Admin' ? null : _selectedDepartment,
        );

        await _userService.createUser(request);
        
        // 显示现代化的成功动画
        SuccessAnimationOverlay.show(
          context,
          title: '创建成功',
          message: '新用户已成功创建',
          onComplete: () {
            Navigator.of(context).pop(true);
          },
        );
        return;
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('操作失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String? _validateUsername(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入用户名';
    }
    if (value.trim().length < 3) {
      return '用户名至少需要3个字符';
    }
    if (value.trim().length > 50) {
      return '用户名不能超过50个字符';
    }
    return null;
  }

  String? _validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return '请输入邮箱地址';
    }

    final email = value.trim();

    // 检查长度
    if (email.length > 254) {
      return '邮箱地址过长';
    }

    // 更严格的邮箱正则表达式
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    );

    if (!emailRegex.hasMatch(email)) {
      return '请输入有效的邮箱地址';
    }

    // 检查是否包含连续的点
    if (email.contains('..')) {
      return '邮箱地址格式不正确';
    }

    // 检查是否以点开头或结尾
    if (email.startsWith('.') || email.endsWith('.')) {
      return '邮箱地址格式不正确';
    }

    return null;
  }

  String? _validatePassword(String? value) {
    if (_isEditing) return null; // Password not required for editing
    
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    if (value.length < 6) {
      return '密码至少需要6个字符';
    }
    if (value.length > 100) {
      return '密码不能超过100个字符';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (_isEditing) return null; // Password not required for editing
    
    if (value == null || value.isEmpty) {
      return '请确认密码';
    }
    if (value != _passwordController.text) {
      return '两次输入的密码不一致';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(_isEditing ? '编辑用户' : '创建用户'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        shadowColor: Colors.transparent,
        leading: Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_rounded, size: 20),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        actions: [
          if (_isLoading)
            Container(
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[600]!),
                ),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildModernBasicInfoSection(),
              const SizedBox(height: 20),
              if (!_isEditing) _buildModernPasswordSection(),
              if (!_isEditing) const SizedBox(height: 20),
              _buildModernAdditionalInfoSection(),
              const SizedBox(height: 32),
              _buildModernActionButtons(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernBasicInfoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.user,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              key: _usernameKey,
              child: _buildModernTextField(
                controller: _usernameController,
                label: '用户名',
                hint: _isEditing ? '用户名（只读）' : '请输入用户名',
                icon: Icons.person_rounded,
                validator: _isEditing ? null : _validateUsername,
                required: !_isEditing,
                enabled: !_isEditing, // 编辑时禁用用户名字段
              ),
            ),
            const SizedBox(height: 16),
            Container(
              key: _emailKey,
              child: _buildModernTextField(
                controller: _emailController,
                label: '邮箱地址',
                hint: _selectedRole == 'Admin' ? '管理员邮箱由系统管理' : '请输入邮箱地址',
                icon: Icons.email_rounded,
                keyboardType: TextInputType.emailAddress,
                validator: _selectedRole == 'Admin' ? null : _validateEmail,
                required: _selectedRole != 'Admin',
                enabled: _selectedRole != 'Admin', // 管理员角色时禁用
              ),
            ),
            const SizedBox(height: 16),
            _buildModernDropdown(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    bool required = false,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            if (required)
              Text(
                ' *',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.red[500],
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          obscureText: obscureText,
          validator: validator,
          enabled: enabled && !_isLoading,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: suffixIcon,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: enabled ? Colors.grey[50] : Colors.grey[100],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildModernDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '角色',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedRole,
            decoration: InputDecoration(
              hintText: '请选择角色',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  FontAwesomeIcons.userShield,
                  color: Colors.grey[600],
                  size: 16,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: _roles.map((role) => DropdownMenuItem<String>(
              value: role,
              child: Text(
                role == 'Admin' ? '管理员' : '普通用户',
                style: const TextStyle(fontSize: 14),
              ),
            )).toList(),
            onChanged: _isLoading ? null : (value) {
              setState(() {
                _selectedRole = value!;
                // 如果选择了管理员角色，清空并锁定相关字段
                if (_selectedRole == 'Admin') {
                  _emailController.clear();
                  _fullNameController.clear();
                  _selectedDepartment = null;
                }
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请选择角色';
              }
              return null;
            },
          ),
        ),
        // 如果选择了管理员角色，显示提示信息
        if (_selectedRole == 'Admin')
          Container(
            margin: const EdgeInsets.only(top: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!, width: 1),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange[600], size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '管理员账户的邮箱、姓名和部门将由系统自动管理',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildModernPasswordSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.lock,
                    color: Colors.orange[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '密码设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              key: _passwordKey,
              child: _buildPasswordFieldWithStrength(),
            ),
            const SizedBox(height: 16),
            Container(
              key: _confirmPasswordKey,
              child: _buildConfirmPasswordField(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPasswordFieldWithStrength() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '密码',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _passwordController,
          obscureText: _obscurePassword,
          validator: _validatePassword,
          enabled: !_isLoading,
          decoration: InputDecoration(
            hintText: '请输入密码（至少6个字符）',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.lock_rounded, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                color: Colors.grey[600],
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _passwordError.isNotEmpty ? Colors.red[400]! : Colors.blue[500]!,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
        // 实时错误提示
        if (_passwordError.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Text(
                _passwordError,
                style: TextStyle(
                  color: Colors.red[400],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
        // 密码强度条
        if (_passwordController.text.isNotEmpty) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              Text(
                '密码强度: ',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                _passwordStrengthText,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w700,
                  color: _passwordStrengthColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _passwordStrength,
              child: Container(
                decoration: BoxDecoration(
                  color: _passwordStrengthColor,
                  borderRadius: BorderRadius.circular(3),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          _buildPasswordRequirements(),
        ],
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    final password = _passwordController.text;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '密码要求:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItem('至少6个字符', password.length >= 6),
          _buildRequirementItem('包含小写字母', password.contains(RegExp(r'[a-z]'))),
          _buildRequirementItem('包含大写字母', password.contains(RegExp(r'[A-Z]'))),
          _buildRequirementItem('包含数字', password.contains(RegExp(r'[0-9]'))),
          _buildRequirementItem('包含特殊字符', password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text, bool satisfied) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(
            satisfied ? Icons.check_circle_rounded : Icons.radio_button_unchecked_rounded,
            color: satisfied ? Colors.green : Colors.grey[400],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: satisfied ? Colors.green : Colors.grey[600],
              fontWeight: satisfied ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConfirmPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '确认密码',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            Text(
              ' *',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.red[500],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _confirmPasswordController,
          obscureText: _obscureConfirmPassword,
          validator: _validateConfirmPassword,
          enabled: !_isLoading,
          decoration: InputDecoration(
            hintText: '请再次输入密码',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.lock_outline_rounded, color: Colors.grey[600], size: 18),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                _obscureConfirmPassword ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                color: Colors.grey[600],
              ),
              onPressed: () {
                setState(() {
                  _obscureConfirmPassword = !_obscureConfirmPassword;
                });
              },
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.grey[300]!,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: _confirmPasswordError.isNotEmpty ? Colors.red[400]! : Colors.blue[500]!,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
        // 实时错误提示
        if (_confirmPasswordError.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Text(
                _confirmPasswordError,
                style: TextStyle(
                  color: Colors.red[400],
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
        // 密码一致性提示
        if (_confirmPasswordController.text.isNotEmpty && _confirmPasswordError.isEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.check_circle_rounded, color: Colors.green, size: 16),
              const SizedBox(width: 8),
              Text(
                '密码一致',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildModernAdditionalInfoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[50],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: FaIcon(
                    FontAwesomeIcons.addressCard,
                    color: Colors.purple[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  '附加信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: Colors.grey[800],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildModernTextField(
              controller: _fullNameController,
              label: '姓名',
              hint: _isEditing
                  ? '姓名（只读）'
                  : _selectedRole == 'Admin'
                      ? '管理员姓名由系统管理'
                      : '请输入真实姓名',
              icon: Icons.badge_rounded,
              validator: (_isEditing || _selectedRole == 'Admin') ? null : (value) {
                if (value != null && value.length > 100) {
                  return '姓名不能超过100个字符';
                }
                return null;
              },
              enabled: !_isEditing && _selectedRole != 'Admin', // 编辑时或管理员角色时禁用
            ),
            const SizedBox(height: 16),
            _buildModernDepartmentField(),
          ],
        ),
      ),
    );
  }

  Widget _buildModernDepartmentField() {
    // 确保当前选择的部门在列表中存在
    List<String> availableDepartments = List.from(_departments);

    // 如果当前选择的部门不在列表中，添加它
    if (_selectedDepartment != null &&
        _selectedDepartment!.isNotEmpty &&
        !availableDepartments.contains(_selectedDepartment)) {
      availableDepartments.add(_selectedDepartment!);
    }

    // 去重并排序
    availableDepartments = availableDepartments.toSet().toList();
    availableDepartments.sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '部门',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: _selectedRole == 'Admin' ? Colors.grey[100] : Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedDepartment,
            decoration: InputDecoration(
              hintText: _selectedRole == 'Admin' ? '管理员部门由系统管理' : '请选择部门',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.business_rounded,
                  color: Colors.purple[600],
                  size: 18,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: [
              const DropdownMenuItem<String>(
                value: null,
                child: Text('请选择部门'),
              ),
              ...availableDepartments.map((dept) => DropdownMenuItem<String>(
                value: dept,
                child: Text(dept),
              )),
            ],
            onChanged: (!_isLoading && _selectedRole != 'Admin') ? (value) {
              setState(() {
                _selectedDepartment = value;
              });
            } : null,
            icon: Icon(
              Icons.arrow_drop_down_rounded,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButtons() {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: TextButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.close_rounded, size: 18, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue[600]!, Colors.blue[700]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _isLoading ? null : _saveUser,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isEditing ? '更新中...' : '创建中...',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          _isEditing ? Icons.update_rounded : Icons.person_add_rounded,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isEditing ? '更新用户' : '创建用户',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ],
    );
  }
} 