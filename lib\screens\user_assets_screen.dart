import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/user_main_layout.dart';
import '../widgets/asset_card.dart';
import '../widgets/asset_filter_sheet.dart';
import '../config/routes.dart';
import '../models/asset.dart';
import '../services/user_dashboard_service.dart';

class UserAssetsScreen extends StatefulWidget {
  const UserAssetsScreen({super.key});

  @override
  State<UserAssetsScreen> createState() => _UserAssetsScreenState();
}

class _UserAssetsScreenState extends State<UserAssetsScreen> {
  final UserDashboardService _userDashboardService = UserDashboardService();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  Timer? _searchTimer;
  
  List<Asset> _assets = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _errorMessage;
  int _totalCount = 0;
  
  // 搜索和筛选参数
  String _searchQuery = '';
  List<String> _selectedCategories = [];
  List<String> _selectedStatuses = [];
  
  // 分页参数
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadAssets();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _hasMoreData) {
        _loadMoreAssets();
      }
    }
  }

  Future<void> _loadAssets({bool refresh = false}) async {
    try {
      setState(() {
        if (refresh) {
          _isLoading = true;
          _currentPage = 1;
          _hasMoreData = true;
        }
        _errorMessage = null;
      });

      final assets = await _userDashboardService.getUserAssets(
        page: _currentPage,
        limit: _pageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        categories: _selectedCategories.isNotEmpty ? _selectedCategories : null,
        statuses: _selectedStatuses.isNotEmpty ? _selectedStatuses : null,
      );

      if (mounted) {
        setState(() {
          if (refresh || _currentPage == 1) {
            _assets = assets;
            _totalCount = assets.length; // 这里应该是实际的总数，但由于我们在前端筛选，暂时用当前数量
          } else {
            _assets.addAll(assets);
            _totalCount = _assets.length;
          }

          _hasMoreData = assets.length == _pageSize;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreAssets() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      _currentPage++;
      final assets = await _userDashboardService.getUserAssets(
        page: _currentPage,
        limit: _pageSize,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
        categories: _selectedCategories.isNotEmpty ? _selectedCategories : null,
        statuses: _selectedStatuses.isNotEmpty ? _selectedStatuses : null,
      );

      if (mounted) {
        setState(() {
          _assets.addAll(assets);
          _hasMoreData = assets.length == _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _currentPage--; // 回退页码
          _isLoadingMore = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('加载更多失败: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return UserMainLayout(
      currentRoute: AppRoutes.userAssets,
      title: '我的资产',
      showBackButton: true,
      child: Column(
        children: [
          _buildSearchAndFilter(),
          Expanded(
            child: _buildAssetList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: '搜索资产名称、编号或序列号...',
                    hintStyle: TextStyle(color: Colors.grey[500]),
                    prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                    filled: true,
                    fillColor: Colors.grey[50],
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[200]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[200]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Theme.of(context).primaryColor),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  ),
                  onChanged: (value) {
                    // 实时搜索，延迟500毫秒避免频繁请求
                    if (_searchTimer?.isActive ?? false) _searchTimer!.cancel();
                    _searchTimer = Timer(const Duration(milliseconds: 500), () {
                      _searchQuery = value;
                      _loadAssets(refresh: true);
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              Container(
                decoration: BoxDecoration(
                  color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
                      ? Theme.of(context).primaryColor.withOpacity(0.1)
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
                        ? Theme.of(context).primaryColor
                        : Colors.grey[200]!,
                  ),
                ),
                child: IconButton(
                  onPressed: _showFilterSheet,
                  icon: Icon(
                    Icons.tune,
                    color: (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
                        ? Theme.of(context).primaryColor
                        : Colors.grey[600],
                  ),
                  tooltip: '筛选',
                ),
              ),
            ],
          ),
          if (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildActiveFilters(),
          ],
          if (!_isLoading && _assets.isNotEmpty) ...[
            const SizedBox(height: 8),
            _buildResultCount(),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveFilters() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          ..._selectedCategories.map((category) => _buildFilterChip(
            label: category,
            onDeleted: () {
              setState(() {
                _selectedCategories.remove(category);
              });
              _loadAssets(refresh: true);
            },
          )),
          ..._selectedStatuses.map((status) => _buildFilterChip(
            label: status,
            onDeleted: () {
              setState(() {
                _selectedStatuses.remove(status);
              });
              _loadAssets(refresh: true);
            },
          )),
          if (_selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty)
            TextButton(
              onPressed: () {
                setState(() {
                  _selectedCategories.clear();
                  _selectedStatuses.clear();
                });
                _loadAssets(refresh: true);
              },
              child: const Text('清除全部'),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({required String label, required VoidCallback onDeleted}) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
            const SizedBox(width: 6),
            GestureDetector(
              onTap: onDeleted,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 14,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultCount() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.inventory_2,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '共找到 ${_assets.length} 项资产',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadAssets(refresh: true),
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (_assets.isEmpty) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.inventory_2_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
              ),
              const SizedBox(height: 24),
              Text(
                '暂无资产',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _searchQuery.isNotEmpty || _selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty
                    ? '没有找到符合条件的资产\n请尝试调整搜索条件或筛选器'
                    : '您还没有被分配任何资产\n请联系管理员进行资产分配',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[500],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              if (_searchQuery.isNotEmpty || _selectedCategories.isNotEmpty || _selectedStatuses.isNotEmpty) ...[
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _selectedCategories.clear();
                      _selectedStatuses.clear();
                      _searchController.clear();
                    });
                    _loadAssets(refresh: true);
                  },
                  icon: const Icon(Icons.clear_all),
                  label: const Text('清除所有筛选'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadAssets(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _assets.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _assets.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final asset = _assets[index];
          return AssetCard(
            asset: asset,
            onTap: () {
              // 导航到用户专用的资产详情页面
              context.go('/user-assets/${asset.id}');
            },
          );
        },
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => AssetFilterSheet(
        selectedCategories: _selectedCategories,
        selectedStatuses: _selectedStatuses,
        onApply: (categories, statuses) {
          setState(() {
            _selectedCategories = categories;
            _selectedStatuses = statuses;
          });
          _loadAssets(refresh: true);
        },
      ),
    );
  }
}
