using System.ComponentModel.DataAnnotations;
using ITAssetAPI.Models;

namespace ITAssetAPI.DTOs
{
    public class TicketDto
    {
        public int Id { get; set; }
        public string TicketNumber { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public int? AssignedToId { get; set; }
        public string? AssignedToName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? AssignedDate { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public List<TicketCommentDto> Comments { get; set; } = new List<TicketCommentDto>();
        public List<TicketAttachmentDto> Attachments { get; set; } = new List<TicketAttachmentDto>();
    }

    public class TicketCommentDto
    {
        public int Id { get; set; }
        public int TicketId { get; set; }
        public int UserId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Content { get; set; } = string.Empty;
        public bool IsInternal { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class TicketAttachmentDto
    {
        public int Id { get; set; }
        public int TicketId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public DateTime UploadedDate { get; set; }
    }

    public class CreateTicketDto
    {
        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;
        
        [Required]
        public string Description { get; set; } = string.Empty;
        
        [Required]
        public string Priority { get; set; } = string.Empty;
        
        [Required]
        public string Category { get; set; } = string.Empty;
    }

    public class UpdateTicketDto
    {
        [MaxLength(200)]
        public string? Title { get; set; }
        
        public string? Description { get; set; }
        
        public string? Status { get; set; }
        
        public string? Priority { get; set; }
        
        public string? Category { get; set; }
        
        public int? AssignedToId { get; set; }
    }

    public class AddCommentDto
    {
        [Required]
        public string Content { get; set; } = string.Empty;
        
        public bool IsInternal { get; set; } = false;
    }

    public class TicketListDto
    {
        public int Id { get; set; }
        public string TicketNumber { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Priority { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string? AssignedToName { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public DateTime? AssignedDate { get; set; }
    }
}
