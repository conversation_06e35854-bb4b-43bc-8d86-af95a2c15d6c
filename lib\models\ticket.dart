class Ticket {
  final int id;
  final String ticketNumber;
  final String title;
  final String description;
  final String status;
  final String priority;
  final String category;
  final int userId;
  final String userName;
  final int? assignedToId;
  final String? assignedToName;
  final DateTime createdDate;
  final DateTime? updatedDate;
  final DateTime? assignedDate;
  final DateTime? resolvedDate;
  final List<TicketComment> comments;
  final List<TicketAttachment> attachments;

  Ticket({
    required this.id,
    required this.ticketNumber,
    required this.title,
    required this.description,
    required this.status,
    required this.priority,
    required this.category,
    required this.userId,
    required this.userName,
    this.assignedToId,
    this.assignedToName,
    required this.createdDate,
    this.updatedDate,
    this.assignedDate,
    this.resolvedDate,
    required this.comments,
    required this.attachments,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) {
    return Ticket(
      id: json['id'],
      ticketNumber: json['ticketNumber'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      status: json['status'] ?? 'Pending',
      priority: json['priority'] ?? 'Medium',
      category: json['category'] ?? 'Other',
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? '',
      assignedToId: json['assignedToId'],
      assignedToName: json['assignedToName'],
      createdDate: DateTime.parse(json['createdDate']),
      updatedDate: json['updatedDate'] != null
          ? DateTime.parse(json['updatedDate'])
          : null,
      assignedDate: json['assignedDate'] != null
          ? DateTime.parse(json['assignedDate'])
          : null,
      resolvedDate: json['resolvedDate'] != null
          ? DateTime.parse(json['resolvedDate'])
          : null,
      comments: (json['comments'] as List<dynamic>?)
          ?.map((item) => TicketComment.fromJson(item))
          .toList() ?? [],
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((item) => TicketAttachment.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketNumber': ticketNumber,
      'title': title,
      'description': description,
      'status': status,
      'priority': priority,
      'category': category,
      'userId': userId,
      'userName': userName,
      'assignedToId': assignedToId,
      'assignedToName': assignedToName,
      'createdDate': createdDate.toIso8601String(),
      'updatedDate': updatedDate?.toIso8601String(),
      'assignedDate': assignedDate?.toIso8601String(),
      'resolvedDate': resolvedDate?.toIso8601String(),
      'comments': comments.map((comment) => comment.toJson()).toList(),
      'attachments': attachments.map((attachment) => attachment.toJson()).toList(),
    };
  }
}

class TicketComment {
  final int id;
  final int ticketId;
  final int userId;
  final String userName;
  final String content;
  final DateTime createdDate;
  final bool isInternal;

  TicketComment({
    required this.id,
    required this.ticketId,
    required this.userId,
    required this.userName,
    required this.content,
    required this.createdDate,
    required this.isInternal,
  });

  factory TicketComment.fromJson(Map<String, dynamic> json) {
    return TicketComment(
      id: json['id'] ?? 0,
      ticketId: json['ticketId'] ?? 0,
      userId: json['userId'] ?? 0,
      userName: json['userName'] ?? '',
      content: json['content'] ?? '',
      createdDate: DateTime.parse(json['createdDate']),
      isInternal: json['isInternal'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'userId': userId,
      'userName': userName,
      'content': content,
      'createdDate': createdDate.toIso8601String(),
      'isInternal': isInternal,
    };
  }
}

class TicketAttachment {
  final int id;
  final int ticketId;
  final String fileName;
  final String filePath;
  final int fileSize;
  final String contentType;
  final DateTime uploadedDate;

  TicketAttachment({
    required this.id,
    required this.ticketId,
    required this.fileName,
    required this.filePath,
    required this.fileSize,
    required this.contentType,
    required this.uploadedDate,
  });

  factory TicketAttachment.fromJson(Map<String, dynamic> json) {
    return TicketAttachment(
      id: json['id'] ?? 0,
      ticketId: json['ticketId'] ?? 0,
      fileName: json['fileName'] ?? '',
      filePath: json['filePath'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      contentType: json['contentType'] ?? '',
      uploadedDate: DateTime.parse(json['uploadedDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ticketId': ticketId,
      'fileName': fileName,
      'filePath': filePath,
      'fileSize': fileSize,
      'contentType': contentType,
      'uploadedDate': uploadedDate.toIso8601String(),
    };
  }
}

class CreateTicketRequest {
  final String title;
  final String description;
  final String priority;
  final String category;

  CreateTicketRequest({
    required this.title,
    required this.description,
    required this.priority,
    required this.category,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'priority': priority,
      'category': category,
    };
  }
}

class TicketStatus {
  static const String pending = 'Pending';
  static const String inProgress = 'InProgress';
  static const String resolved = 'Resolved';
  static const String closed = 'Closed';
}

class TicketPriority {
  static const String low = 'Low';
  static const String medium = 'Medium';
  static const String high = 'High';
}

class TicketCategory {
  static const String hardware = 'Hardware';
  static const String software = 'Software';
  static const String network = 'Network';
  static const String account = 'Account';
  static const String other = 'Other';
}
