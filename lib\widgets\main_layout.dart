import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../config/routes.dart';
import '../providers/auth_provider.dart';

class MainLayout extends StatefulWidget {
  final Widget child;
  final String currentRoute;
  final bool showBackButton;
  final String? title;
  final Widget? floatingActionButton;
  
  const MainLayout({
    super.key,
    required this.child,
    required this.currentRoute,
    this.showBackButton = false,
    this.title,
    this.floatingActionButton,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _getCurrentIndex() {
    switch (widget.currentRoute) {
      case AppRoutes.dashboard:
        return 0;
      case AppRoutes.assetList:
        return 1;
      case AppRoutes.activityLog:
        return 2;
      case '/users':
        return 3;
      default:
        // 对于子页面，根据父页面确定索引
        if (widget.currentRoute.startsWith('/assets')) {
          return 1; // 资产相关页面
        } else if (widget.currentRoute.startsWith('/users')) {
          return 3; // 用户管理相关页面
        }
        return 0; // 默认仪表板
    }
  }

  void _onTabTapped(int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.dashboard);
        break;
      case 1:
        context.go(AppRoutes.assetList);
        break;
      case 2:
        context.go(AppRoutes.activityLog);
        break;
      case 3:
        context.go('/users');
        break;
    }
  }

  String _getTitle() {
    if (widget.title != null) {
      return widget.title!;
    }
    
    switch (widget.currentRoute) {
      case AppRoutes.dashboard:
        return '仪表板';
      case AppRoutes.assetList:
        return '资产管理';
      case AppRoutes.activityLog:
        return '活动日志';
      case '/users':
        return '用户管理';
      default:
        if (widget.currentRoute.startsWith('/assets')) {
          if (widget.currentRoute.contains('/new')) {
            return '创建资产';
          } else if (widget.currentRoute.contains('/edit')) {
            return '编辑资产';
          } else if (widget.currentRoute.contains('/assets/') && !widget.currentRoute.contains('/edit')) {
            return '资产详情';
          }
          return '资产管理';
        } else if (widget.currentRoute.startsWith('/users')) {
          if (widget.currentRoute.contains('/new')) {
            return '创建用户';
          } else if (widget.currentRoute.contains('/edit')) {
            return '编辑用户';
          } else if (widget.currentRoute.contains('/users/') && !widget.currentRoute.contains('/edit')) {
            return '用户详情';
          }
          return '用户管理';
        }
        return 'IT资产管理';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_getTitle()),
        leading: widget.showBackButton
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                  } else {
                    // 如果无法返回，根据当前页面跳转到合适的父页面
                    if (widget.currentRoute.startsWith('/assets')) {
                      context.go(AppRoutes.assetList);
                    } else {
                      context.go(AppRoutes.dashboard);
                    }
                  }
                },
              )
            : null,
        actions: [
          IconButton(
            onPressed: () async {
              final confirmed = await showDialog<bool>(
                context: context,
                builder: (context) => AlertDialog(
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                  title: Row(
                    children: [
                      Icon(Icons.logout, color: Colors.orange[600]),
                      const SizedBox(width: 8),
                      const Text('确认退出'),
                    ],
                  ),
                  content: const Text('您确定要退出登录吗？'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      child: Text(
                        '取消',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('退出'),
                    ),
                  ],
                ),
              );

              if (confirmed == true) {
                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.logout();
                if (context.mounted) {
                  context.go(AppRoutes.login);
                }
              }
            },
            icon: const Icon(Icons.logout),
            tooltip: '退出登录',
          ),
        ],
      ),
      body: widget.child,
      floatingActionButton: widget.floatingActionButton,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _getCurrentIndex(),
        onTap: _onTabTapped,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: '仪表板',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: '资产管理',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.history),
            label: '活动日志',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.people),
            label: '用户管理',
          ),
        ],
      ),
    );
  }
} 