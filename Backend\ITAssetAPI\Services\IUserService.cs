using ITAssetAPI.DTOs;

namespace ITAssetAPI.Services
{
    public interface IUserService
    {
        Task<UserListDto> GetUsersAsync(UserQueryDto query);
        Task<UserDto?> GetUserByIdAsync(int id);
        Task<UserDto?> GetUserByUsernameAsync(string username);
        Task<UserDto> CreateUserAsync(CreateUserDto createUserDto);
        Task<UserDto?> UpdateUserAsync(int id, UpdateUserDto updateUserDto);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ChangePasswordAsync(int id, ChangePasswordDto changePasswordDto);
        Task<bool> UserExistsAsync(int id);
        Task<bool> UsernameExistsAsync(string username, int? excludeId = null);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
        Task<List<string>> GetRolesAsync();
        Task<List<string>> GetDepartmentsAsync();
        Task<bool> AssignRoleAsync(int userId, string role);
        Task<bool> ChangeUserRoleAsync(int userId, string role, int currentUserId);
        Task<bool> ToggleUserStatusAsync(int userId, bool isActive);
        Task<UserDto?> UpdateAvatarAsync(int userId, string? avatarUrl);
        Task<string> UploadAvatarAsync(int userId, IFormFile file);
    }
} 