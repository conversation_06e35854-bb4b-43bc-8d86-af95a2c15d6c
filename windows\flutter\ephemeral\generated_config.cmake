# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Android\\StudioProject\\ITAssetApp" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter"
  "PROJECT_DIR=D:\\Android\\StudioProject\\ITAssetApp"
  "FLUTTER_ROOT=D:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Android\\StudioProject\\ITAssetApp\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Android\\StudioProject\\ITAssetApp"
  "FLUTTER_TARGET=D:\\Android\\StudioProject\\ITAssetApp\\lib\\main.dart"
  "DART_DEFINES=Zmx1dHRlci5pbnNwZWN0b3Iuc3RydWN0dXJlZEVycm9ycz10cnVl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Android\\StudioProject\\ITAssetApp\\.dart_tool\\package_config.json"
)
