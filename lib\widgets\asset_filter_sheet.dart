import 'package:flutter/material.dart';
import '../models/asset.dart';

class AssetFilterSheet extends StatefulWidget {
  final List<String> selectedCategories;
  final List<String> selectedStatuses;
  final Function(List<String> categories, List<String> statuses) onApply;

  const AssetFilterSheet({
    super.key,
    required this.selectedCategories,
    required this.selectedStatuses,
    required this.onApply,
  });

  @override
  State<AssetFilterSheet> createState() => _AssetFilterSheetState();
}

class _AssetFilterSheetState extends State<AssetFilterSheet> {
  late List<String> _selectedCategories;
  late List<String> _selectedStatuses;

  final List<String> _categories = [
    'laptop',
    'desktop',
    'monitor',
    'printer',
    'phone',
    'tablet',
    'server',
    'network',
    'other',
  ];

  final List<String> _statuses = [
    'available',
    'assigned',
    'maintenance',
    'retired',
  ];

  @override
  void initState() {
    super.initState();
    _selectedCategories = List.from(widget.selectedCategories);
    _selectedStatuses = List.from(widget.selectedStatuses);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  '筛选条件',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      _selectedCategories.clear();
                      _selectedStatuses.clear();
                    });
                  },
                  child: const Text('清除全部'),
                ),
              ],
            ),
          ),
          
          // 筛选内容
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 资产类别
                  _buildSectionTitle('资产类别'),
                  const SizedBox(height: 8),
                  _buildCategoryFilters(),
                  
                  const SizedBox(height: 24),
                  
                  // 资产状态
                  _buildSectionTitle('资产状态'),
                  const SizedBox(height: 8),
                  _buildStatusFilters(),
                  
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('取消'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      widget.onApply(_selectedCategories, _selectedStatuses);
                      Navigator.of(context).pop();
                    },
                    child: const Text('应用'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildCategoryFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _categories.map((category) {
        final isSelected = _selectedCategories.contains(category);
        return FilterChip(
          label: Text(_getCategoryDisplayName(category)),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedCategories.add(category);
              } else {
                _selectedCategories.remove(category);
              }
            });
          },
          selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
          checkmarkColor: Theme.of(context).primaryColor,
        );
      }).toList(),
    );
  }

  Widget _buildStatusFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _statuses.map((status) {
        final isSelected = _selectedStatuses.contains(status);
        return FilterChip(
          label: Text(_getStatusDisplayName(status)),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              if (selected) {
                _selectedStatuses.add(status);
              } else {
                _selectedStatuses.remove(status);
              }
            });
          },
          selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
          checkmarkColor: Theme.of(context).primaryColor,
        );
      }).toList(),
    );
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'laptop':
        return '笔记本电脑';
      case 'desktop':
        return '台式电脑';
      case 'monitor':
        return '显示器';
      case 'printer':
        return '打印机';
      case 'phone':
        return '手机';
      case 'tablet':
        return '平板电脑';
      case 'server':
        return '服务器';
      case 'network':
        return '网络设备';
      case 'other':
        return '其他';
      default:
        return category;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status) {
      case 'available':
        return '可用';
      case 'assigned':
        return '已分配';
      case 'maintenance':
        return '维护中';
      case 'retired':
        return '已报废';
      default:
        return status;
    }
  }
}
