using ITAssetAPI.DTOs;
using ITAssetAPI.Models;

namespace ITAssetAPI.Services
{
    public interface IAssetService
    {
        Task<AssetListResponseDto> GetAssetsAsync(int page = 1, int pageSize = 20, string? search = null, string[]? categories = null, string[]? statuses = null);
        Task<AssetListResponseDto> GetUserAssetsAsync(int userId, int page = 1, int pageSize = 20, string? search = null, string[]? categories = null, string[]? statuses = null);
        Task<UserAssetStatsDto> GetUserAssetStatsAsync(int userId);
        Task<AssetDto?> GetAssetByIdAsync(int id);
        Task<AssetDto> CreateAssetAsync(CreateAssetDto createAssetDto);
        Task<AssetDto?> UpdateAssetAsync(int id, UpdateAssetDto updateAssetDto);
        Task<bool> DeleteAssetAsync(int id);
        Task<string> GenerateNextAssetNumberAsync(AssetCategory category);
    }
}