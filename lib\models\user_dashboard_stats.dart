class UserDashboardStats {
  final int assignedToMeCount;
  final int activeAssetsCount;
  final int maintenanceAssetsCount;
  final int pendingTicketsCount;
  final int completedTicketsCount;
  final List<UserAssetSummary> recentAssets;
  final List<UserTicketSummary> recentTickets;

  UserDashboardStats({
    required this.assignedToMeCount,
    required this.activeAssetsCount,
    required this.maintenanceAssetsCount,
    required this.pendingTicketsCount,
    required this.completedTicketsCount,
    required this.recentAssets,
    required this.recentTickets,
  });

  factory UserDashboardStats.fromJson(Map<String, dynamic> json) {
    return UserDashboardStats(
      assignedToMeCount: json['assignedToMeCount'] ?? 0,
      activeAssetsCount: json['activeAssetsCount'] ?? 0,
      maintenanceAssetsCount: json['maintenanceAssetsCount'] ?? 0,
      pendingTicketsCount: json['pendingTicketsCount'] ?? 0,
      completedTicketsCount: json['completedTicketsCount'] ?? 0,
      recentAssets: (json['recentAssets'] as List<dynamic>?)
          ?.map((item) => UserAssetSummary.fromJson(item))
          .toList() ?? [],
      recentTickets: (json['recentTickets'] as List<dynamic>?)
          ?.map((item) => UserTicketSummary.fromJson(item))
          .toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'assignedToMeCount': assignedToMeCount,
      'activeAssetsCount': activeAssetsCount,
      'maintenanceAssetsCount': maintenanceAssetsCount,
      'pendingTicketsCount': pendingTicketsCount,
      'completedTicketsCount': completedTicketsCount,
      'recentAssets': recentAssets.map((asset) => asset.toJson()).toList(),
      'recentTickets': recentTickets.map((ticket) => ticket.toJson()).toList(),
    };
  }
}

class UserAssetSummary {
  final int id;
  final String name;
  final String assetNumber;
  final String category;
  final String status;
  final DateTime assignedDate;

  UserAssetSummary({
    required this.id,
    required this.name,
    required this.assetNumber,
    required this.category,
    required this.status,
    required this.assignedDate,
  });

  factory UserAssetSummary.fromJson(Map<String, dynamic> json) {
    return UserAssetSummary(
      id: json['id'],
      name: json['name'],
      assetNumber: json['assetNumber'],
      category: json['category'],
      status: json['status'],
      assignedDate: DateTime.parse(json['assignedDate']),
    );
  }

  factory UserAssetSummary.fromAsset(dynamic asset) {
    return UserAssetSummary(
      id: int.tryParse(asset.id?.toString() ?? '0') ?? 0,
      name: asset.name,
      assetNumber: asset.assetNumber,
      category: asset.category.toString().split('.').last,
      status: asset.status.toString().split('.').last,
      assignedDate: asset.updatedAt ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'assetNumber': assetNumber,
      'category': category,
      'status': status,
      'assignedDate': assignedDate.toIso8601String(),
    };
  }
}

class UserTicketSummary {
  final int id;
  final String title;
  final String status;
  final String priority;
  final DateTime createdDate;
  final DateTime? updatedDate;

  UserTicketSummary({
    required this.id,
    required this.title,
    required this.status,
    required this.priority,
    required this.createdDate,
    this.updatedDate,
  });

  factory UserTicketSummary.fromJson(Map<String, dynamic> json) {
    return UserTicketSummary(
      id: json['id'],
      title: json['title'],
      status: json['status'],
      priority: json['priority'],
      createdDate: DateTime.parse(json['createdDate']),
      updatedDate: json['updatedDate'] != null 
          ? DateTime.parse(json['updatedDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'status': status,
      'priority': priority,
      'createdDate': createdDate.toIso8601String(),
      'updatedDate': updatedDate?.toIso8601String(),
    };
  }
}
