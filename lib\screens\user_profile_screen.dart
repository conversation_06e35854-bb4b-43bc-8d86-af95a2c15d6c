import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../providers/auth_provider.dart';
import '../widgets/user_main_layout.dart';
import '../config/routes.dart';
import '../config/api_config.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import '../widgets/success_animation.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key});

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordFormKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  final UserService _userService = UserService();
  bool _isLoading = false;
  bool _isEditMode = false;
  bool _showPasswordSection = false;

  // 密码可见性控制
  bool _obscureCurrentPassword = true;
  bool _obscureNewPassword = true;
  bool _obscureConfirmPassword = true;

  // 密码验证状态
  String _currentPasswordError = '';
  String _newPasswordError = '';
  String _confirmPasswordError = '';
  double _passwordStrength = 0.0;
  String _passwordStrengthText = '';
  Color _passwordStrengthColor = Colors.red;

  // 部门相关
  String? _selectedDepartment;
  List<String> _departments = [];
  bool _isDepartmentLoading = false;

  // 头像相关
  final ImagePicker _imagePicker = ImagePicker();
  bool _isUploadingAvatar = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
    _loadDepartments();

    // 添加密码实时验证监听器
    _newPasswordController.addListener(_validatePasswordRealtime);
    _confirmPasswordController.addListener(_validateConfirmPasswordRealtime);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 获取完整的头像URL
  String _getFullAvatarUrl(String avatarUrl) {
    print('Processing avatar URL: $avatarUrl');
    if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
      print('Full URL detected: $avatarUrl');
      return avatarUrl;
    } else {
      // 相对URL，需要添加基础URL
      final fullUrl = '${ApiConfig.baseUrl}$avatarUrl';
      print('Relative URL converted to: $fullUrl');
      return fullUrl;
    }
  }

  void _loadUserData() {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final user = authProvider.currentUser;

    if (user != null) {
      print('Loading user data:');
      print('Username: ${user.username}');
      print('Avatar URL: ${user.avatarUrl}');
      print('Avatar URL is empty: ${user.avatarUrl?.isEmpty ?? true}');
      
      _usernameController.text = user.username;
      _fullNameController.text = user.fullName ?? '';
      _emailController.text = user.email;
      _selectedDepartment = user.department;
    } else {
      print('No current user found');
    }
  }

  Future<void> _loadDepartments() async {
    setState(() {
      _isDepartmentLoading = true;
    });

    try {
      final departments = await _userService.getDepartments();

      // 添加默认部门选项
      final defaultDepartments = [
        'IT部门',
        '财务部门',
        '人事部门',
        '市场部门',
        '销售部门',
        '运营部门',
        '客服部门',
        '研发部门',
        '行政部门',
        '法务部门',
      ];

      // 合并API返回的部门和默认部门，去重
      final allDepartments = <String>{};
      allDepartments.addAll(departments);
      allDepartments.addAll(defaultDepartments);

      setState(() {
        _departments = allDepartments.toList()..sort();
        _isDepartmentLoading = false;
      });
    } catch (e) {
      // 如果API失败，至少提供默认部门
      setState(() {
        _departments = [
          'IT部门',
          '财务部门',
          '人事部门',
          '市场部门',
          '销售部门',
          '运营部门',
          '客服部门',
          '研发部门',
          '行政部门',
          '法务部门',
        ];
        _isDepartmentLoading = false;
      });
    }
  }

  // 实时密码验证
  void _validatePasswordRealtime() {
    final password = _newPasswordController.text;
    setState(() {
      _passwordStrength = _calculatePasswordStrength(password);
      _passwordStrengthText = _getPasswordStrengthText(_passwordStrength);
      _passwordStrengthColor = _getPasswordStrengthColor(_passwordStrength);

      if (password.isNotEmpty && password.length < 6) {
        _newPasswordError = '密码至少需要6个字符';
      } else if (password.isNotEmpty && password == _currentPasswordController.text) {
        _newPasswordError = '新密码不能与当前密码相同';
      } else {
        _newPasswordError = '';
      }
    });

    // 如果确认密码已输入，也要重新验证一致性
    if (_confirmPasswordController.text.isNotEmpty) {
      _validateConfirmPasswordRealtime();
    }
  }

  void _validateConfirmPasswordRealtime() {
    final confirmPassword = _confirmPasswordController.text;
    final newPassword = _newPasswordController.text;

    setState(() {
      if (confirmPassword.isNotEmpty && confirmPassword != newPassword) {
        _confirmPasswordError = '两次输入的密码不一致';
      } else {
        _confirmPasswordError = '';
      }
    });
  }

  // 计算密码强度
  double _calculatePasswordStrength(String password) {
    if (password.isEmpty) return 0.0;

    double strength = 0.0;

    // 长度检查
    if (password.length >= 6) strength += 0.2;
    if (password.length >= 8) strength += 0.1;
    if (password.length >= 12) strength += 0.1;

    // 包含小写字母
    if (password.contains(RegExp(r'[a-z]'))) strength += 0.15;

    // 包含大写字母
    if (password.contains(RegExp(r'[A-Z]'))) strength += 0.15;

    // 包含数字
    if (password.contains(RegExp(r'[0-9]'))) strength += 0.15;

    // 包含特殊字符
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) strength += 0.15;

    return strength.clamp(0.0, 1.0);
  }

  String _getPasswordStrengthText(double strength) {
    if (strength == 0.0) return '';
    if (strength < 0.3) return '弱';
    if (strength < 0.6) return '中等';
    if (strength < 0.8) return '强';
    return '很强';
  }

  Color _getPasswordStrengthColor(double strength) {
    if (strength == 0.0) return Colors.grey;
    if (strength < 0.3) return Colors.red;
    if (strength < 0.6) return Colors.orange;
    if (strength < 0.8) return Colors.blue;
    return Colors.green;
  }

  @override
  Widget build(BuildContext context) {
    return UserMainLayout(
      currentRoute: AppRoutes.userProfile,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.blue[50]!,
              Colors.white,
            ],
          ),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildModernProfileHeader(),
              const SizedBox(height: 24),
              _buildModernProfileForm(),
              const SizedBox(height: 24),
              _buildModernPasswordSection(),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernProfileHeader() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.blue[600]!,
                Colors.blue[700]!,
                Colors.indigo[600]!,
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.3),
                spreadRadius: 0,
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: _buildAvatarWidget(user),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.blue[600],
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            spreadRadius: 0,
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: _isUploadingAvatar ? null : _showAvatarOptions,
                        icon: _isUploadingAvatar
                            ? const SizedBox(
                                width: 12,
                                height: 12,
                                child: CircularProgressIndicator(
                                  strokeWidth: 1.5,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(
                                Icons.camera_alt,
                                color: Colors.white,
                                size: 14,
                              ),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Text(
                user?.username ?? '未知用户',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              // 只有当用户名不存在时才显示真实姓名
              if ((user?.username == null || user!.username.isEmpty) &&
                  user?.fullName != null && user!.fullName!.isNotEmpty) ...[
                const SizedBox(height: 4),
                Text(
                  user.fullName!,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.envelope,
                    color: Colors.white70,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    user?.email ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.white.withOpacity(0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const FaIcon(
                      FontAwesomeIcons.userTag,
                      color: Colors.white,
                      size: 14,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      user?.role ?? '普通用户',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernProfileForm() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.green[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: FaIcon(
                      FontAwesomeIcons.userEdit,
                      color: Colors.green[600],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '个人信息',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        Text(
                          '管理您的个人资料信息',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: _isEditMode ? Colors.orange[50] : Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _isEditMode = !_isEditMode;
                          if (!_isEditMode) {
                            _loadUserData(); // 取消编辑时重新加载数据
                          }
                        });
                      },
                      icon: Icon(
                        _isEditMode ? Icons.cancel_rounded : Icons.edit_rounded,
                        color: _isEditMode ? Colors.orange[600] : Colors.blue[600],
                      ),
                      label: Text(
                        _isEditMode ? '取消' : '编辑',
                        style: TextStyle(
                          color: _isEditMode ? Colors.orange[600] : Colors.blue[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildModernTextField(
                controller: _usernameController,
                label: '用户名',
                hint: '请输入您的用户名',
                icon: FontAwesomeIcons.userTag,
                enabled: _isEditMode,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入用户名';
                  }
                  if (value.trim().length < 3) {
                    return '用户名至少需要3个字符';
                  }
                  if (value.trim().length > 50) {
                    return '用户名不能超过50个字符';
                  }
                  // 用户名只能包含字母、数字、下划线
                  if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value.trim())) {
                    return '用户名只能包含字母、数字和下划线';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              _buildModernTextField(
                controller: _fullNameController,
                label: '真实姓名',
                hint: '请输入您的真实姓名（可选）',
                icon: FontAwesomeIcons.user,
                enabled: _isEditMode,
                validator: (value) {
                  if (value != null && value.trim().length > 100) {
                    return '姓名不能超过100个字符';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              _buildModernTextField(
                controller: _emailController,
                label: '邮箱地址',
                hint: '请输入您的邮箱地址',
                icon: FontAwesomeIcons.envelope,
                enabled: _isEditMode,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入邮箱地址';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value.trim())) {
                    return '请输入有效的邮箱地址';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              _buildModernDepartmentDropdown(),
              if (_isEditMode) ...[
                const SizedBox(height: 32),
                _buildModernSaveButton(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    bool enabled = true,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          enabled: enabled,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: enabled ? Colors.blue[50] : Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: FaIcon(
                icon,
                color: enabled ? Colors.blue[600] : Colors.grey[400],
                size: 16,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.blue[500]!, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[200]!, width: 1),
            ),
            filled: true,
            fillColor: enabled ? Colors.grey[50] : Colors.grey[100],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildModernDepartmentDropdown() {
    // 确保当前选择的部门在列表中存在
    List<String> availableDepartments = List.from(_departments);

    // 如果当前选择的部门不在列表中，添加它
    if (_selectedDepartment != null &&
        _selectedDepartment!.isNotEmpty &&
        !availableDepartments.contains(_selectedDepartment)) {
      availableDepartments.add(_selectedDepartment!);
    }

    // 去重并排序
    availableDepartments = availableDepartments.toSet().toList();
    availableDepartments.sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '部门',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: _isEditMode ? Colors.grey[50] : Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedDepartment,
            decoration: InputDecoration(
              hintText: '请选择部门',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _isEditMode ? Colors.purple[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: FaIcon(
                  FontAwesomeIcons.building,
                  color: _isEditMode ? Colors.purple[600] : Colors.grey[400],
                  size: 16,
                ),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: [
              const DropdownMenuItem<String>(
                value: null,
                child: Text('请选择部门'),
              ),
              ...availableDepartments.map((dept) => DropdownMenuItem<String>(
                value: dept,
                child: Text(dept),
              )),
            ],
            onChanged: _isEditMode ? (value) {
              setState(() {
                _selectedDepartment = value;
              });
            } : null,
            icon: _isDepartmentLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(
                    Icons.arrow_drop_down_rounded,
                    color: _isEditMode ? Colors.grey[600] : Colors.grey[400],
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernSaveButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[600]!, Colors.blue[700]!],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveProfile,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.floppyDisk,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '保存更改',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildModernPasswordSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _passwordFormKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: FaIcon(
                      FontAwesomeIcons.lock,
                      color: Colors.orange[600],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '密码设置',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ),
                        ),
                        Text(
                          '修改您的登录密码',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: _showPasswordSection ? Colors.red[50] : Colors.orange[50],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: TextButton.icon(
                      onPressed: () {
                        setState(() {
                          _showPasswordSection = !_showPasswordSection;
                          if (!_showPasswordSection) {
                            _currentPasswordController.clear();
                            _newPasswordController.clear();
                            _confirmPasswordController.clear();
                            _currentPasswordError = '';
                            _newPasswordError = '';
                            _confirmPasswordError = '';
                            _passwordStrength = 0.0;
                            _passwordStrengthText = '';
                          }
                        });
                      },
                      icon: Icon(
                        _showPasswordSection ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                        color: _showPasswordSection ? Colors.red[600] : Colors.orange[600],
                      ),
                      label: Text(
                        _showPasswordSection ? '隐藏' : '修改密码',
                        style: TextStyle(
                          color: _showPasswordSection ? Colors.red[600] : Colors.orange[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (_showPasswordSection) ...[
                const SizedBox(height: 24),
                _buildModernPasswordField(
                  controller: _currentPasswordController,
                  label: '当前密码',
                  hint: '请输入当前密码',
                  obscureText: _obscureCurrentPassword,
                  onToggleVisibility: () {
                    setState(() {
                      _obscureCurrentPassword = !_obscureCurrentPassword;
                    });
                  },
                  errorText: _currentPasswordError,
                  onChanged: (value) {
                    // 清除之前的错误信息
                    if (_currentPasswordError.isNotEmpty) {
                      setState(() {
                        _currentPasswordError = '';
                      });
                    }

                    // 如果新密码已输入，检查是否与当前密码相同
                    if (_newPasswordController.text.isNotEmpty &&
                        _newPasswordController.text == value) {
                      setState(() {
                        _newPasswordError = '新密码不能与当前密码相同';
                      });
                    } else if (_newPasswordError == '新密码不能与当前密码相同') {
                      setState(() {
                        _newPasswordError = '';
                      });
                    }
                  },
                ),
                const SizedBox(height: 20),
                _buildModernPasswordField(
                  controller: _newPasswordController,
                  label: '新密码',
                  hint: '请输入新密码（至少6个字符）',
                  obscureText: _obscureNewPassword,
                  onToggleVisibility: () {
                    setState(() {
                      _obscureNewPassword = !_obscureNewPassword;
                    });
                  },
                  errorText: _newPasswordError,
                ),
                // 密码强度条
                if (_newPasswordController.text.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Text(
                        '密码强度: ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        _passwordStrengthText,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                          color: _passwordStrengthColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    height: 6,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: FractionallySizedBox(
                      alignment: Alignment.centerLeft,
                      widthFactor: _passwordStrength,
                      child: Container(
                        decoration: BoxDecoration(
                          color: _passwordStrengthColor,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildPasswordRequirements(),
                ],
                const SizedBox(height: 20),
                _buildModernPasswordField(
                  controller: _confirmPasswordController,
                  label: '确认新密码',
                  hint: '请再次输入新密码',
                  obscureText: _obscureConfirmPassword,
                  onToggleVisibility: () {
                    setState(() {
                      _obscureConfirmPassword = !_obscureConfirmPassword;
                    });
                  },
                  errorText: _confirmPasswordError,
                ),
                // 密码一致性提示
                if (_confirmPasswordController.text.isNotEmpty && _confirmPasswordError.isEmpty) ...[
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(Icons.check_circle_rounded, color: Colors.green, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '密码一致',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
                const SizedBox(height: 32),
                _buildModernChangePasswordButton(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernPasswordField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required bool obscureText,
    required VoidCallback onToggleVisibility,
    String errorText = '',
    Function(String)? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          obscureText: obscureText,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            prefixIcon: Container(
              margin: const EdgeInsets.all(12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: FaIcon(
                FontAwesomeIcons.lock,
                color: Colors.orange[600],
                size: 16,
              ),
            ),
            suffixIcon: IconButton(
              icon: Icon(
                obscureText ? Icons.visibility_off_rounded : Icons.visibility_rounded,
                color: Colors.grey[600],
              ),
              onPressed: onToggleVisibility,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.orange[500]!, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.red[400]!, width: 2),
            ),
            filled: true,
            fillColor: Colors.grey[50],
            contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          ),
        ),
        // 实时错误提示
        if (errorText.isNotEmpty) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.error_outline_rounded, color: Colors.red[400], size: 16),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  errorText,
                  style: TextStyle(
                    color: Colors.red[400],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    final password = _newPasswordController.text;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '密码要求:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildRequirementItem('至少6个字符', password.length >= 6),
          _buildRequirementItem('包含小写字母', password.contains(RegExp(r'[a-z]'))),
          _buildRequirementItem('包含大写字母', password.contains(RegExp(r'[A-Z]'))),
          _buildRequirementItem('包含数字', password.contains(RegExp(r'[0-9]'))),
          _buildRequirementItem('包含特殊字符', password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))),
        ],
      ),
    );
  }

  Widget _buildRequirementItem(String text, bool isMet) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            isMet ? Icons.check_circle_rounded : Icons.radio_button_unchecked_rounded,
            color: isMet ? Colors.green : Colors.grey[400],
            size: 16,
          ),
          const SizedBox(width: 8),
          Text(
            text,
            style: TextStyle(
              fontSize: 11,
              color: isMet ? Colors.green : Colors.grey[600],
              fontWeight: isMet ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernChangePasswordButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange[600]!, Colors.orange[700]!],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _changePassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 24,
                width: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.key,
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '修改密码',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        throw Exception('用户信息不存在');
      }

      // 创建更新请求
      final request = UpdateProfileRequest(
        username: _usernameController.text.trim(),
        email: _emailController.text.trim(),
        fullName: _fullNameController.text.trim().isEmpty
            ? null
            : _fullNameController.text.trim(),
        department: _selectedDepartment,
      );

      await _userService.updateProfile(request);

      // 显示现代化的成功动画，不自动跳转
      SuccessAnimationOverlay.show(
        context,
        title: '更新成功',
        message: '您的个人信息已成功更新',
        onComplete: () {
          setState(() {
            _isEditMode = false;
          });
          // 在动画完成后再刷新用户信息，避免触发路由重定向
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted) {
              authProvider.refreshUser().then((_) {
                if (mounted) {
                  _loadUserData();
                }
              });
            }
          });
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text('更新失败: $e')),
            ],
          ),
          backgroundColor: Colors.red[600],
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _changePassword() async {
    // 验证所有字段
    if (_currentPasswordController.text.isEmpty) {
      setState(() {
        _currentPasswordError = '请输入当前密码';
      });
      return;
    }

    if (_newPasswordController.text.isEmpty) {
      setState(() {
        _newPasswordError = '请输入新密码';
      });
      return;
    }

    if (_confirmPasswordController.text.isEmpty) {
      setState(() {
        _confirmPasswordError = '请确认新密码';
      });
      return;
    }

    if (_newPasswordController.text != _confirmPasswordController.text) {
      setState(() {
        _confirmPasswordError = '两次输入的密码不一致';
      });
      return;
    }

    if (_newPasswordController.text.length < 6) {
      setState(() {
        _newPasswordError = '密码至少需要6个字符';
      });
      return;
    }

    if (_newPasswordController.text == _currentPasswordController.text) {
      setState(() {
        _newPasswordError = '新密码不能与当前密码相同';
      });
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final currentUser = authProvider.currentUser;

      if (currentUser == null) {
        throw Exception('用户信息不存在');
      }

      final request = ChangePasswordRequest(
        currentPassword: _currentPasswordController.text,
        newPassword: _newPasswordController.text,
      );

      await _userService.changeMyPassword(request);

      // 显示现代化的成功动画
      SuccessAnimationOverlay.show(
        context,
        title: '密码修改成功',
        message: '您的密码已成功更新',
        onComplete: () {
          setState(() {
            _showPasswordSection = false;
          });

          _currentPasswordController.clear();
          _newPasswordController.clear();
          _confirmPasswordController.clear();
          _currentPasswordError = '';
          _newPasswordError = '';
          _confirmPasswordError = '';
          _passwordStrength = 0.0;
          _passwordStrengthText = '';
        },
      );
    } catch (e) {
      // 统一显示当前密码错误提示
      setState(() {
        _currentPasswordError = '当前密码存在错误';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 显示头像选项
  void _showAvatarOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              '更换头像',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAvatarOption(
                  icon: Icons.camera_alt,
                  label: '拍照',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.camera);
                  },
                ),
                _buildAvatarOption(
                  icon: Icons.photo_library,
                  label: '相册',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.gallery);
                  },
                ),
                _buildAvatarOption(
                  icon: Icons.delete,
                  label: '删除',
                  onTap: () {
                    Navigator.pop(context);
                    _deleteAvatar();
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.blue[50],
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.blue[600],
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  // 选择图片
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await _uploadAvatar(File(image.path));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('选择图片失败: $e'),
          backgroundColor: Colors.red[600],
        ),
      );
    }
  }

  // 上传头像
  Future<void> _uploadAvatar(File imageFile) async {
    setState(() {
      _isUploadingAvatar = true;
    });

    try {
      // 上传头像文件
      final avatarUrl = await _userService.uploadAvatar(imageFile);
      print('Avatar uploaded successfully: $avatarUrl');
      
      // 显示成功消息
      SuccessAnimationOverlay.show(
        context,
        title: '上传成功',
        message: '头像已成功更新',
        onComplete: () async {
          // 在动画完成后刷新用户信息
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          await authProvider.refreshUser();
          
          if (mounted) {
            setState(() {
              _loadUserData();
            });
          }
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('上传头像失败: $e'),
          backgroundColor: Colors.red[600],
        ),
      );
    } finally {
      setState(() {
        _isUploadingAvatar = false;
      });
    }
  }

  // 删除头像
  Future<void> _deleteAvatar() async {
    setState(() {
      _isUploadingAvatar = true;
    });

    try {
      // 删除头像
      await _userService.deleteAvatar();

      SuccessAnimationOverlay.show(
        context,
        title: '删除成功',
        message: '头像已成功删除',
        onComplete: () async {
          // 在动画完成后刷新用户信息
          final authProvider = Provider.of<AuthProvider>(context, listen: false);
          await authProvider.refreshUser();
          
          if (mounted) {
            setState(() {
              _loadUserData();
            });
          }
        },
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('删除头像失败: $e'),
          backgroundColor: Colors.red[600],
        ),
      );
    } finally {
      setState(() {
        _isUploadingAvatar = false;
      });
    }
  }

  // 构建头像组件
  Widget _buildAvatarWidget(user) {
    final hasAvatar = user?.avatarUrl != null && user!.avatarUrl!.isNotEmpty;
    
    if (hasAvatar) {
      return CircleAvatar(
        radius: 45,
        backgroundColor: Colors.white,
        backgroundImage: NetworkImage(_getFullAvatarUrl(user.avatarUrl!)),
        onBackgroundImageError: (exception, stackTrace) {
          print('Avatar image load error: $exception');
          print('Stack trace: $stackTrace');
        },
      );
    } else {
      return CircleAvatar(
        radius: 45,
        backgroundColor: Colors.white,
        child: Text(
          (user?.username ?? 'U')[0].toUpperCase(),
          style: TextStyle(
            fontSize: 36,
            fontWeight: FontWeight.bold,
            color: Colors.blue[600],
          ),
        ),
      );
    }
  }
}
