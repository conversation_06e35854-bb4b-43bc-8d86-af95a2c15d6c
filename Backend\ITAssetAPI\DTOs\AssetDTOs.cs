using System.ComponentModel.DataAnnotations;
using ITAssetAPI.Models;

namespace ITAssetAPI.DTOs
{
    public class AssetDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string AssetNumber { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string? Brand { get; set; }
        public string? Model { get; set; }
        public string? SerialNumber { get; set; }
        public string? AssignedTo { get; set; }
        public int? AssignedUserId { get; set; }
        public DateTime? PurchaseDate { get; set; }
        public decimal? Value { get; set; }
        public string? Vendor { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class CreateAssetDto
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [MaxLength(50)]
        public string? AssetNumber { get; set; }
        
        [Required]
        public AssetCategory Category { get; set; }
        
        public AssetStatus Status { get; set; } = AssetStatus.Available;
        
        [MaxLength(50)]
        public string? Brand { get; set; }
        
        [MaxLength(100)]
        public string? Model { get; set; }
        
        [MaxLength(100)]
        public string? SerialNumber { get; set; }
        
        [MaxLength(100)]
        public string? AssignedTo { get; set; }
        
        public int? AssignedUserId { get; set; }
        
        public DateTime? PurchaseDate { get; set; }
        
        public decimal? Value { get; set; }
        
        [MaxLength(100)]
        public string? Vendor { get; set; }
        
        public string? Description { get; set; }
        
        [MaxLength(100)]
        public string? Location { get; set; }
        
        public DateTime? LastMaintenanceDate { get; set; }
        
        public DateTime? NextMaintenanceDate { get; set; }
    }

    public class UpdateAssetDto
    {
        [MaxLength(100)]
        public string? Name { get; set; }
        
        public AssetCategory? Category { get; set; }
        
        public AssetStatus? Status { get; set; }
        
        [MaxLength(50)]
        public string? Brand { get; set; }
        
        [MaxLength(100)]
        public string? Model { get; set; }
        
        [MaxLength(100)]
        public string? SerialNumber { get; set; }
        
        [MaxLength(100)]
        public string? AssignedTo { get; set; }
        
        public int? AssignedUserId { get; set; }
        
        public DateTime? PurchaseDate { get; set; }
        
        public decimal? Value { get; set; }
        
        [MaxLength(100)]
        public string? Vendor { get; set; }
        
        public string? Description { get; set; }
        
        [MaxLength(100)]
        public string? Location { get; set; }
        
        public DateTime? LastMaintenanceDate { get; set; }
        
        public DateTime? NextMaintenanceDate { get; set; }
    }

    public class AssetListResponseDto
    {
        public IEnumerable<AssetDto> Assets { get; set; } = new List<AssetDto>();
        public int TotalCount { get; set; }
        public int Page { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class UserAssetStatsDto
    {
        public int AssignedToMeCount { get; set; }
        public int ActiveAssetsCount { get; set; }
        public int MaintenanceAssetsCount { get; set; }
        public int RetiredAssetsCount { get; set; }
    }
} 